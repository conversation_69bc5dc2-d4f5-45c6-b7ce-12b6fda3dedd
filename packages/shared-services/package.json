{"name": "@anter/shared-services", "version": "0.0.1", "private": true, "description": "Shared services for the Anter monorepo.", "main": "dist/index.js", "types": "dist/types/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc && cp -r src/config dist/", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"pino": "^9.9.5"}, "devDependencies": {"@types/node": "^24.3.3", "typescript": "^5.9.2", "vitest": "^3.2.4"}}