import * as fs from 'fs';
import * as path from 'path';
import { SharedEnv } from '../config/env';

interface OrganizationPromptsConfig {
  organizationPrompts: Record<string, string>;
  defaultPrompt: string;
}

/**
 * Service for resolving organization IDs to their corresponding Langfuse prompt names.
 * Implements singleton pattern for efficient configuration loading.
 */
export class OrganizationPromptResolver {
  private static instance: OrganizationPromptResolver;
  private organizationPrompts: Record<string, string>;
  private defaultPrompt: string;

  private constructor() {
    // Try multiple possible locations for the config file
    const possiblePaths = [
      path.join(__dirname, '../config/organization-prompts.json'), // Production (dist)
      path.join(__dirname, '../../src/config/organization-prompts.json'), // Development
      path.join(process.cwd(), 'packages/shared-services/src/config/organization-prompts.json'), // Fallback
    ];

    let configPath: string | null = null;
    let configData: string;

    // Find the first existing config file
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        configPath = possiblePath;
        break;
      }
    }

    if (!configPath) {
      throw new Error(
        `Organization prompts config file not found. Tried paths: ${possiblePaths.join(', ')}`
      );
    }

    try {
      configData = fs.readFileSync(configPath, 'utf8');
    } catch (error) {
      throw new Error(`Failed to read organization prompts config from ${configPath}: ${error}`);
    }

    const config: OrganizationPromptsConfig = JSON.parse(configData);

    this.organizationPrompts = config.organizationPrompts;
    this.defaultPrompt = config.defaultPrompt;
  }

  /**
   * Get the singleton instance of OrganizationPromptResolver
   */
  public static getInstance(): OrganizationPromptResolver {
    if (!OrganizationPromptResolver.instance) {
      OrganizationPromptResolver.instance = new OrganizationPromptResolver();
    }
    return OrganizationPromptResolver.instance;
  }

  /**
   * Resolve an organization ID to its corresponding Langfuse prompt name.
   *
   * Resolution order:
   * 1. Check organization-specific mapping in JSON config
   * 2. Fall back to LANGFUSE_DEFAULT_PROMPT environment variable
   * 3. Final fallback to default prompt from JSON config
   *
   * @param organizationId - The UUID-based organization identifier
   * @returns The Langfuse prompt name to use for this organization
   */
  public getPromptNameForOrganization(organizationId: string): string {
    // 1. Check organization-specific mapping
    const orgSpecificPrompt = this.organizationPrompts[organizationId];
    if (orgSpecificPrompt) {
      return orgSpecificPrompt;
    }

    // 2. Fall back to environment variable
    if (SharedEnv.langfuseDefaultPrompt && SharedEnv.langfuseDefaultPrompt !== 'system-default') {
      return SharedEnv.langfuseDefaultPrompt;
    }

    // 3. Final fallback to config default
    return this.defaultPrompt;
  }

  /**
   * Get all configured organization prompt mappings
   * Useful for debugging and administration
   */
  public getAllMappings(): Record<string, string> {
    return { ...this.organizationPrompts };
  }

  /**
   * Check if an organization has a specific prompt mapping
   */
  public hasOrganizationMapping(organizationId: string): boolean {
    return organizationId in this.organizationPrompts;
  }

  /**
   * Get the default prompt name
   */
  public getDefaultPrompt(): string {
    return this.defaultPrompt;
  }
}
