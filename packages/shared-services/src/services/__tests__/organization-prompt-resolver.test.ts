import { describe, it, expect, beforeEach } from 'vitest';
import { OrganizationPromptResolver } from '../organization-prompt-resolver';

describe('OrganizationPromptResolver', () => {
  let resolver: OrganizationPromptResolver;

  beforeEach(() => {
    resolver = OrganizationPromptResolver.getInstance();
  });

  it('should return singleton instance', () => {
    const instance1 = OrganizationPromptResolver.getInstance();
    const instance2 = OrganizationPromptResolver.getInstance();
    expect(instance1).toBe(instance2);
  });

  it('should return organization-specific prompt for mapped organization', () => {
    const promptName = resolver.getPromptNameForOrganization('cme9fz85h0006n96hrv7psmo9');
    expect(promptName).toBe('ask-ai-general');
  });

  it('should return organization-specific prompt for example org 1', () => {
    const promptName = resolver.getPromptNameForOrganization('example-org-id-1');
    expect(promptName).toBe('custom-security-prompt');
  });

  it('should return organization-specific prompt for example org 2', () => {
    const promptName = resolver.getPromptNameForOrganization('example-org-id-2');
    expect(promptName).toBe('custom-finance-prompt');
  });

  it('should return default prompt for unmapped organization', () => {
    const promptName = resolver.getPromptNameForOrganization('unknown-org-id');
    expect(promptName).toBe('ask-ai-general');
  });

  it('should check if organization has mapping', () => {
    expect(resolver.hasOrganizationMapping('cme9fz85h0006n96hrv7psmo9')).toBe(true);
    expect(resolver.hasOrganizationMapping('unknown-org-id')).toBe(false);
  });

  it('should return all mappings', () => {
    const mappings = resolver.getAllMappings();
    expect(mappings).toEqual({
      cme9fz85h0006n96hrv7psmo9: 'ask-ai-general',
      'example-org-id-1': 'custom-security-prompt',
      'example-org-id-2': 'custom-finance-prompt',
    });
  });

  it('should return default prompt', () => {
    expect(resolver.getDefaultPrompt()).toBe('ask-ai-general');
  });
});
