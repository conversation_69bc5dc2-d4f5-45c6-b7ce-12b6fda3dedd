import { FastifyInstance, FastifyReply } from 'fastify';
import { getActualAgentName } from '../../../../config/agent-routing';
import {
  formatNonStreamingResponse,
  formatStreamingCompletionResponse,
  formatErrorResponse,
} from '../../../../lib/utils/response-formatter';
import {
  AgentInvokeRequest,
  AgentStreamRequest,
  AgentInvokeResponse,
  StreamChunk,
  AgentInput,
} from '../schemas/agents/types';

/**
 * Service class for handling agent operations
 * Extracted from route handlers to separate business logic from HTTP handling
 */
export class AgentService {
  constructor(private fastify: FastifyInstance) {}

  /**
   * Validate session ID and log appropriate warnings/info
   */
  private validateSessionId(context: any, requestId: string, agentName: string): void {
    if (!context?.sessionId) {
      this.fastify.log.warn(
        'No sessionId provided in request context - conversation memory will not be available',
        {
          requestId,
          agentName,
          hasContext: !!context,
          contextKeys: context ? Object.keys(context) : [],
        }
      );
    } else {
      this.fastify.log.info('Session ID provided for conversation memory', {
        requestId,
        sessionId: context.sessionId,
        agentName,
      });
    }
  }

  /**
   * Invoke an agent with the provided input and context
   */
  async invokeAgent(request: AgentInvokeRequest, requestId: string): Promise<AgentInvokeResponse> {
    const { agent_name, input, context } = request;
    const startTime = Date.now();

    this.fastify.log.info('[/agent/invoke] context:', context);

    // Validate session ID for conversation memory
    this.validateSessionId(context, requestId, agent_name);

    // Use routing configuration to determine which agent to use
    const actualAgentName = getActualAgentName(agent_name);

    if (actualAgentName !== agent_name) {
      this.fastify.log.info(`Agent routing: ${agent_name} -> ${actualAgentName}`);
    }

    try {
      // Use the agents system from the plugin
      const result = await this.fastify.agents.invoke(actualAgentName, input, {
        sessionId: context?.sessionId,
        organizationId: context?.organizationId,
        userId: context?.userId,
        metadata: {
          ...context?.metadata,
          requestId: requestId,
          originalAgentName: agent_name,
          actualAgentName: actualAgentName,
        },
      });

      const executionTime = Date.now() - startTime;

      const formattedResponse = formatNonStreamingResponse(
        result,
        executionTime,
        actualAgentName,
        requestId
      );

      // Debug log: Print sources from the response object that clients will see
      const sources = formattedResponse?.result?.metadata?.sources || [];
      this.fastify.log.info('DEBUG - Sources in client response (single-shot):', {
        requestId: requestId,
        agentName: actualAgentName,
        sourcesCount: sources.length,
        sources: JSON.stringify(sources, null, 2),
      });

      return formattedResponse;
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : undefined;

      // Log error details in a more visible format
      this.fastify.log.error(`Agent invocation failed: ${errorMessage}`);
      if (errorStack) {
        this.fastify.log.error(`Error stack: ${errorStack}`);
      }

      // Also log structured data for debugging
      this.fastify.log.error('Agent invocation failed:', {
        error: errorMessage,
        stack: errorStack,
        agentName: agent_name,
        actualAgentName,
        requestId: requestId,
      });

      // Determine error type and throw appropriate error
      if (error.message.includes('not found') || error.message.includes('Unknown agent')) {
        throw new AgentNotFoundError(`Agent '${agent_name}' not found`);
      }

      throw new AgentInvocationError('Agent invocation failed');
    }
  }

  /**
   * Stream agent responses using Server-Sent Events
   */
  async streamAgent(
    request: AgentStreamRequest,
    requestId: string,
    reply: FastifyReply
  ): Promise<void> {
    const { agent_name, input, context } = request;

    // Validate session ID for conversation memory
    this.validateSessionId(context, requestId, agent_name);

    // Resolve to the streaming-capable agent variant
    const actualAgentName = agent_name === 'ask_ai' ? 'ask_ai_streaming' : agent_name;

    // Check if agent supports streaming
    const agent = await this.fastify.agents.create(actualAgentName, ['streaming']);
    if (!agent) {
      throw new AgentNotFoundError(`Agent '${actualAgentName}' not found`);
    }

    if (!agent.capabilities.includes('streaming')) {
      throw new StreamingNotSupportedError(`Agent '${agent_name}' does not support streaming`);
    }

    // Set enhanced SSE headers with security considerations
    reply.raw.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      Pragma: 'no-cache',
      Expires: '0',
      Connection: 'keep-alive',
      'X-Request-ID': requestId,
      'X-Trace-ID': requestId,
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control, Content-Type, Authorization',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Max-Age': '86400',
    });

    // Send initial connection established event
    reply.raw.write(
      `data: ${JSON.stringify({
        type: 'connection_established',
        timestamp: new Date().toISOString(),
        requestId: requestId,
        agentName: actualAgentName,
        capabilities: agent.capabilities,
      })}\n\n`
    );

    await this.handleAgentStreaming(
      agent,
      input,
      context,
      requestId,
      reply,
      actualAgentName,
      agent_name
    );
  }

  /**
   * Handle the actual streaming process
   */
  private async handleAgentStreaming(
    agent: any,
    input: any,
    context: any,
    requestId: string,
    reply: FastifyReply,
    actualAgentName: string,
    originalAgentName: string
  ): Promise<void> {
    // Declare variables outside try block for use in catch block
    const startTime = Date.now();
    let fullResponse = '';
    let searchResults: any = null;
    let confidenceData: any = null;
    let finalMetadata: any = null;
    let chunkCount = 0;
    let lastChunkTime = Date.now();

    try {
      // Construct proper AgentInput structure
      const agentInput: AgentInput = {
        input: input,
        sessionId: context?.sessionId,
        organizationId: context?.organizationId,
        userId: context?.userId,
        traceId: requestId,
        metadata: {
          ...context?.metadata,
          requestId: requestId,
          timestamp: new Date().toISOString(),
        },
      };

      // Stream the agent response with timeout protection
      this.fastify.log.info(`Starting streaming for request ${requestId}`, {
        requestId: requestId,
        agentName: actualAgentName,
        startTime: new Date().toISOString(),
      });

      let hasReceivedChunks = false;
      for await (const chunk of agent.stream(agentInput)) {
        const currentTime = Date.now();
        hasReceivedChunks = true;

        // Check for connection timeout (60 seconds without data)
        if (currentTime - lastChunkTime > 60000) {
          this.fastify.log.warn(`Streaming timeout for request ${requestId}`);
          reply.raw.write(
            `data: ${JSON.stringify({
              type: 'error',
              content: 'Connection timeout - please try again',
              timestamp: new Date().toISOString(),
            })}\n\n`
          );
          break;
        }

        lastChunkTime = currentTime;
        chunkCount++;

        const eventData: StreamChunk = {
          type: chunk.type,
          content: chunk.content,
          metadata: {
            ...chunk.metadata,
            chunkIndex: chunkCount,
            elapsedMs: currentTime - startTime,
          },
          timestamp: new Date().toISOString(),
        };

        // Process different chunk types
        const chunkResult = await this.processStreamChunk(
          chunk,
          eventData,
          reply,
          requestId,
          actualAgentName,
          chunkCount,
          startTime
        );

        // Update accumulated data
        if (chunkResult) {
          if (chunkResult.fullResponse !== undefined) fullResponse += chunkResult.fullResponse;
          if (chunkResult.searchResults !== undefined) searchResults = chunkResult.searchResults;
          if (chunkResult.confidenceData !== undefined) confidenceData = chunkResult.confidenceData;
          if (chunkResult.finalMetadata !== undefined) finalMetadata = chunkResult.finalMetadata;
        }

        // Handle completion or error chunks
        if (chunk.type === 'complete' || chunk.type === 'error') {
          return;
        }

        // Send the chunk with rate limiting (max 100 chunks per second)
        if (chunkCount % 100 === 0) {
          await new Promise(resolve => setTimeout(resolve, 10));
        }

        reply.raw.write(`data: ${JSON.stringify(eventData)}\n\n`);
      }

      // Handle completion if no chunks were received or streaming ended normally
      await this.handleStreamCompletion(
        hasReceivedChunks,
        fullResponse,
        searchResults,
        confidenceData,
        finalMetadata,
        startTime,
        chunkCount,
        actualAgentName,
        requestId,
        reply
      );
    } catch (error: any) {
      await this.handleStreamError(
        error,
        startTime,
        chunkCount,
        requestId,
        actualAgentName,
        originalAgentName,
        reply
      );
    }
  }

  /**
   * Process individual stream chunks
   */
  private async processStreamChunk(
    chunk: any,
    eventData: StreamChunk,
    reply: FastifyReply,
    requestId: string,
    actualAgentName: string,
    chunkCount: number,
    startTime: number
  ): Promise<{
    fullResponse?: string;
    searchResults?: any;
    confidenceData?: any;
    finalMetadata?: any;
  } | null> {
    // Accumulate data for final response
    switch (chunk.type) {
      case 'content':
        return { fullResponse: chunk.content };
      case 'metadata':
        const result: any = {};
        if (chunk.content?.searchResults) {
          result.searchResults = chunk.content.searchResults;
        }
        if (chunk.content?.confidence) {
          result.confidenceData = chunk.content.confidence;
        }
        if (chunk.content?.finalResult) {
          result.finalMetadata = chunk.content.finalResult;
        }
        return Object.keys(result).length > 0 ? result : null;
      case 'complete':
        // Agent has signaled completion - this is the final chunk
        this.fastify.log.info(`Agent completion chunk received for request ${requestId}`, {
          requestId: requestId,
          chunkIndex: chunkCount,
          metadata: JSON.stringify(chunk.metadata, null, 2),
        });

        // Debug log: Print sources from the completion chunk that clients will see
        const completionSources = chunk.metadata?.sources || [];
        this.fastify.log.info('DEBUG - Sources in client response (streaming completion):', {
          requestId: requestId,
          agentName: actualAgentName,
          sourcesCount: completionSources.length,
          sources: JSON.stringify(completionSources, null, 2),
        });

        // Send the completion chunk as-is (agent already formatted it)
        reply.raw.write(`data: ${JSON.stringify(eventData)}\n\n`);
        reply.raw.end();
        return null;
      case 'error':
        this.fastify.log.error(`Error chunk received for request ${requestId}`, {
          requestId: requestId,
          errorContent: chunk.content,
          chunkIndex: chunkCount,
        });

        // Send error event
        reply.raw.write(
          `data: ${JSON.stringify({
            type: 'error',
            content: chunk.content,
            metadata: chunk.metadata,
            timestamp: new Date().toISOString(),
          })}\n\n`
        );

        // Send completion event
        reply.raw.write(
          `data: ${JSON.stringify({
            type: 'complete',
            content: '',
            metadata: {
              error: true,
              executionTimeMs: Date.now() - startTime,
              totalChunks: chunkCount,
            },
            timestamp: new Date().toISOString(),
            sources: chunk.metadata?.sources,
          })}\n\n`
        );

        reply.raw.end();
        return null;

      default:
        this.fastify.log.warn(`Unknown chunk type for request ${requestId}`, {
          requestId: requestId,
          chunkType: chunk.type,
          chunkIndex: chunkCount,
        });
        return null;
    }
  }

  /**
   * Handle stream completion
   */
  private async handleStreamCompletion(
    hasReceivedChunks: boolean,
    fullResponse: string,
    searchResults: any,
    confidenceData: any,
    finalMetadata: any,
    startTime: number,
    chunkCount: number,
    actualAgentName: string,
    requestId: string,
    reply: FastifyReply
  ): Promise<void> {
    this.fastify.log.info(`Streaming loop completed for request ${requestId}`, {
      requestId: requestId,
      totalChunks: chunkCount,
      hasReceivedChunks,
      elapsedMs: Date.now() - startTime,
    });

    // Ensure we always send a completion signal, even if no chunks were received
    if (!hasReceivedChunks) {
      this.fastify.log.warn(
        `No chunks received for request ${requestId}, sending empty completion`,
        {
          requestId: requestId,
          elapsedMs: Date.now() - startTime,
        }
      );

      const emptyCompletionData = formatStreamingCompletionResponse({
        fullResponse: '',
        searchResults: null,
        confidenceData: null,
        finalMetadata: null,
        executionTimeMs: Date.now() - startTime,
        totalChunks: 0,
        averageChunkTimeMs: 0,
        agentName: actualAgentName,
        requestId: requestId,
        hasReceivedChunks: false,
      });

      this.fastify.log.info(`Sending empty completion event for request ${requestId}`, {
        requestId: requestId,
        totalChunks: 0,
        hasReceivedChunks: false,
      });

      reply.raw.write(`data: ${JSON.stringify(emptyCompletionData)}\n\n`);
      reply.raw.end();

      this.fastify.log.info(`Empty streaming completed for request ${requestId}`, {
        requestId: requestId,
        finalChunkCount: 0,
        finalExecutionTime: Date.now() - startTime,
      });
      return;
    }

    // Send completion event with consistent format
    const completionData = formatStreamingCompletionResponse({
      fullResponse,
      searchResults,
      confidenceData,
      finalMetadata,
      executionTimeMs: Date.now() - startTime,
      totalChunks: chunkCount,
      averageChunkTimeMs: chunkCount > 0 ? (Date.now() - startTime) / chunkCount : 0,
      agentName: actualAgentName,
      requestId: requestId,
      hasReceivedChunks,
    });

    // Debug log: Print sources from the completion data that clients will see
    const completionSources = completionData?.metadata?.sources || [];
    this.fastify.log.info('DEBUG - Sources in client response (streaming fallback completion):', {
      requestId: requestId,
      agentName: actualAgentName,
      sourcesCount: completionSources.length,
      sources: JSON.stringify(completionSources, null, 2),
    });

    this.fastify.log.info(`Sending completion event for request ${requestId}`, {
      requestId: requestId,
      totalChunks: chunkCount,
      hasReceivedChunks,
      executionTimeMs: Date.now() - startTime,
      hasFullResponse: !!fullResponse,
      hasSearchResults: !!searchResults,
    });

    reply.raw.write(`data: ${JSON.stringify(completionData)}\n\n`);
    reply.raw.end();

    this.fastify.log.info(`Streaming completed successfully for request ${requestId}`, {
      requestId: requestId,
      finalChunkCount: chunkCount,
      finalExecutionTime: Date.now() - startTime,
    });
  }

  /**
   * Handle streaming errors
   */
  private async handleStreamError(
    error: any,
    startTime: number,
    chunkCount: number,
    requestId: string,
    actualAgentName: string,
    originalAgentName: string,
    reply: FastifyReply
  ): Promise<void> {
    this.fastify.log.error('Agent streaming failed:', error, {
      requestId: requestId,
      agentName: actualAgentName,
      errorName: error.name,
      errorMessage: error.message,
      errorStack: error.stack,
      elapsedMs: Date.now() - startTime,
      totalChunks: chunkCount,
    });

    // Send error event with detailed error information
    const errorData = {
      type: 'error',
      content: error.message || 'An error occurred during streaming',
      metadata: {
        errorType: error.name || 'UNKNOWN_ERROR',
        errorCode: error.code || 'STREAMING_ERROR',
        elapsedMs: Date.now() - startTime,
        requestId: requestId,
        totalChunks: chunkCount,
      },
      timestamp: new Date().toISOString(),
    };

    this.fastify.log.info(`Sending error event for request ${requestId}`, {
      requestId: requestId,
      errorType: errorData.metadata.errorType,
      errorCode: errorData.metadata.errorCode,
    });

    reply.raw.write(`data: ${JSON.stringify(errorData)}\n\n`);

    // Send completion event with error information
    const errorCompletionData = formatErrorResponse(
      error,
      Date.now() - startTime,
      originalAgentName,
      requestId,
      chunkCount
    );

    this.fastify.log.info(`Sending error completion event for request ${requestId}`, {
      requestId: requestId,
      errorMessage: errorCompletionData.metadata.error_message,
    });

    reply.raw.write(`data: ${JSON.stringify(errorCompletionData)}\n\n`);
    reply.raw.end();

    this.fastify.log.info(`Error streaming completed for request ${requestId}`, {
      requestId: requestId,
      finalChunkCount: chunkCount,
      finalExecutionTime: Date.now() - startTime,
    });
  }
}

/**
 * Custom error classes for agent operations
 */
export class AgentNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AgentNotFoundError';
  }
}

export class AgentInvocationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AgentInvocationError';
  }
}

export class StreamingNotSupportedError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'StreamingNotSupportedError';
  }
}
