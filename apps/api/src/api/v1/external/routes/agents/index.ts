import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import {
  AgentService,
  AgentNotFoundError,
  AgentInvocationError,
  StreamingNotSupportedError,
} from '../../services/agents.services';
import {
  agentInvokeSchema,
  agentStreamSchema,
  AgentInvokeRequest,
  AgentStreamRequest,
} from '../../schemas/agents';

export default async function externalAgentRoutes(fastify: FastifyInstance): Promise<void> {
  // Create agent service instance
  const agentService = new AgentService(fastify);

  // POST /invoke - Invoke an agent with input
  fastify.post('/invoke', {
    schema: agentInvokeSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const requestBody = request.body as AgentInvokeRequest;

      try {
        const response = await agentService.invokeAgent(requestBody, request.id);
        return response;
      } catch (error: any) {
        if (error instanceof AgentNotFoundError) {
          return reply.code(400).send({
            error: error.message,
            code: 'AGENT_NOT_FOUND',
          });
        }

        if (error instanceof AgentInvocationError) {
          return reply.code(500).send({
            error: error.message,
            code: 'AGENT_INVOCATION_ERROR',
          });
        }

        // Handle unexpected errors
        fastify.log.error('Unexpected error in agent invocation:', error);
        return reply.code(500).send({
          error: 'An unexpected error occurred',
          code: 'INTERNAL_ERROR',
        });
      }
    },
  });

  // POST /stream - Stream agent responses using Server-Sent Events
  fastify.post('/stream', {
    schema: agentStreamSchema,
    // Authentication handled by dual auth middleware
    handler: async (request: FastifyRequest, reply: FastifyReply) => {
      const requestBody = request.body as AgentStreamRequest;

      try {
        await agentService.streamAgent(requestBody, request.id, reply);
      } catch (error: any) {
        if (error instanceof AgentNotFoundError) {
          return reply.code(400).send({
            error: error.message,
            code: 'AGENT_NOT_FOUND',
          });
        }

        if (error instanceof StreamingNotSupportedError) {
          return reply.code(400).send({
            error: error.message,
            code: 'STREAMING_NOT_SUPPORTED',
          });
        }

        // Handle unexpected errors
        fastify.log.error('Unexpected error in agent streaming:', error);
        return reply.code(500).send({
          error: 'An unexpected error occurred during streaming',
          code: 'STREAMING_ERROR',
        });
      }
    },
  });
}
