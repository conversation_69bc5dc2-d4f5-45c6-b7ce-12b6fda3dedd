/**
 * TypeScript type definitions for agent API requests and responses
 */

/**
 * Request body for agent invocation
 */
export interface AgentInvokeRequest {
  agent_name: string;
  input: any;
  context?: {
    sessionId?: string;
    organizationId?: string;
    userId?: string;
    metadata?: Record<string, any>;
    [key: string]: any;
  };
}

/**
 * Request body for agent streaming
 */
export interface AgentStreamRequest {
  agent_name: string;
  input: any;
  context?: {
    sessionId?: string;
    organizationId?: string;
    userId?: string;
    metadata?: Record<string, any>;
    [key: string]: any;
  };
}

/**
 * Standard error response structure
 */
export interface AgentErrorResponse {
  error: string;
  code: string;
}

/**
 * Success response for agent invocation
 */
export interface AgentInvokeResponse {
  success: boolean;
  result: {
    type: string;
    content: any;
    metadata: {
      session_id: string | null;
      agent_name: string;
      request_id: string;
      semantic_search: {
        results_count: number;
        average_score: number;
        relevance_quality: string;
        context_doc_ids: string[];
        executor: string;
      };
      sources: any[];
      confidence: {
        score: number;
        reason: string;
      } | null;
      performance: {
        execution_time_ms: number;
        total_chunks: number;
        average_chunk_time_ms: number;
        embedding_calls: number;
        tokens_used: number;
        conversation_entries: number;
      };
    };
    timestamp: string;
  };
  metadata: {
    agent_name: string;
    execution_time: number;
    timestamp: string;
  };
}

/**
 * Stream chunk data structure
 */
export interface StreamChunk {
  type: 'connection_established' | 'content' | 'metadata' | 'complete' | 'error';
  content?: any;
  metadata?: Record<string, any>;
  timestamp: string;
  requestId?: string;
  agentName?: string;
  capabilities?: string[];
}

/**
 * Agent context structure for internal processing
 */
export interface AgentContext {
  sessionId?: string;
  organizationId?: string;
  userId?: string;
  metadata?: Record<string, any>;
  requestId?: string;
  originalAgentName?: string;
  actualAgentName?: string;
}

/**
 * Agent input structure for service layer
 */
export interface AgentInput {
  input: any;
  sessionId?: string;
  organizationId?: string;
  userId?: string;
  traceId?: string;
  metadata?: Record<string, any>;
}
