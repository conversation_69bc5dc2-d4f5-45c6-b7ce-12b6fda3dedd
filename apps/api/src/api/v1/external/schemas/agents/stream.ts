// Schema definitions for agent streaming endpoints

/**
 * Schema for agent streaming request body
 */
export const agentStreamRequestSchema = {
  type: 'object',
  required: ['agent_name', 'input'],
  properties: {
    agent_name: {
      type: 'string',
      description: 'Name of the agent to invoke',
      examples: ['ask_ai', 'echo', 'chat'],
    },
    input: {
      description: 'Input data for the agent (can be string, object, or any type)',
    },
    context: {
      type: 'object',
      description: 'Optional context for the agent',
      additionalProperties: true,
    },
  },
  additionalProperties: false,
};

/**
 * Complete schema for agent stream endpoint
 */
export const agentStreamSchema = {
  tags: ['Agents'],
  summary: 'Stream agent responses',
  description: 'Stream AI agent responses using Server-Sent Events (SSE)',
  body: agentStreamRequestSchema,
} as const;
