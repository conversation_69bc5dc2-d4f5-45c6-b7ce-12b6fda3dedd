// Schema definitions for agent invocation endpoints

/**
 * Schema for agent invocation request body
 */
export const agentInvokeRequestSchema = {
  type: 'object',
  required: ['agent_name', 'input'],
  properties: {
    agent_name: {
      type: 'string',
      description: 'Name of the agent to invoke',
      examples: ['echo', 'chat', 'security-analyst', 'ask_ai'],
    },
    input: {
      description: 'Input data for the agent (can be string, object, or any type)',
      // Allow any type - let the agent handle validation
    },
    context: {
      type: 'object',
      description: 'Optional context for the agent',
      additionalProperties: true,
    },
  },
  additionalProperties: false,
};

/**
 * Schema for successful agent invocation response
 */
export const agentInvokeSuccessResponseSchema = {
  type: 'object',
  properties: {
    success: { type: 'boolean' },
    result: {
      type: 'object',
      additionalProperties: true,
    },
    metadata: {
      type: 'object',
      properties: {
        agent_name: { type: 'string' },
        original_agent_name: { type: 'string' },
        execution_time: { type: 'number' },
        timestamp: { type: 'string' },
      },
    },
  },
};

/**
 * Schema for agent error responses
 */
export const agentErrorResponseSchema = {
  type: 'object',
  properties: {
    error: { type: 'string' },
    code: { type: 'string' },
  },
};

/**
 * Complete schema for agent invoke endpoint
 */
export const agentInvokeSchema = {
  tags: ['Agents'],
  summary: 'Invoke an agent',
  description: 'Invoke an AI agent with provided input and context',
  body: agentInvokeRequestSchema,
  response: {
    200: agentInvokeSuccessResponseSchema,
    400: agentErrorResponseSchema,
    500: agentErrorResponseSchema,
  },
} as const;
