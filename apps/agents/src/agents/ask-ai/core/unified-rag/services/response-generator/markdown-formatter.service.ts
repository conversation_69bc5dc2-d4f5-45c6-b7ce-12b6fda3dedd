import { ILogger } from '@/integration/mcp-server/mcp-bridge-enhanced';

/**
 * Service responsible for ensuring perfect markdown formatting of AI responses
 */
export class MarkdownFormatterService {
  constructor(private logger: ILogger) {
    this.logger.info('[MarkdownFormatterService] initialized');
  }

  /**
   * Formats the AI response to ensure perfect markdown formatting
   * @param response - The raw AI response
   * @returns Formatted markdown response
   */
  formatResponse(response: string): string {
    if (!response || typeof response !== 'string') {
      return response;
    }

    this.logger.debug('[MarkdownFormatterService] Formatting response', {
      originalLength: response.length,
    });

    try {
      let formattedResponse = response;

      // Fix numbered lists - ensure proper spacing and formatting
      formattedResponse = this.fixNumberedLists(formattedResponse);

      // Fix bullet lists
      formattedResponse = this.fixBulletLists(formattedResponse);

      // Fix headers
      formattedResponse = this.fixHeaders(formattedResponse);

      // Fix code blocks
      formattedResponse = this.fixCodeBlocks(formattedResponse);

      // Fix inline code
      formattedResponse = this.fixInlineCode(formattedResponse);

      // Fix bold and italic text
      formattedResponse = this.fixBoldItalic(formattedResponse);

      // Fix links
      formattedResponse = this.fixLinks(formattedResponse);

      // Clean up extra whitespace
      formattedResponse = this.cleanupWhitespace(formattedResponse);

      this.logger.debug('[MarkdownFormatterService] Response formatted successfully', {
        originalLength: response.length,
        formattedLength: formattedResponse.length,
        changes: formattedResponse !== response,
      });

      return formattedResponse;
    } catch (error) {
      this.logger.error(
        '[MarkdownFormatterService] Error formatting response',
        error instanceof Error ? error.message : String(error)
      );
      return response; // Return original response if formatting fails
    }
  }

  /**
   * Fix numbered lists to ensure proper markdown formatting
   */
  private fixNumberedLists(text: string): string {
    // Pattern to match numbered lists that might be malformed
    // This handles cases like "1. **Title:** Description" or "1. Title: Description"
    const numberedListPattern = /^(\s*)(\d+)\.\s*(\*\*[^*]+\*\*:?)\s*(.*)$/gm;

    return text.replace(numberedListPattern, (_match, indent, number, title, description) => {
      // Clean up the title (remove extra bold formatting if needed)
      const cleanTitle = title.replace(/\*\*/g, '').trim();

      // Ensure proper spacing
      const formattedTitle = `**${cleanTitle}**`;
      const formattedDescription = description.trim();

      // Return properly formatted numbered list item
      return `${indent}${number}. ${formattedTitle}${formattedDescription ? ': ' + formattedDescription : ''}`;
    });
  }

  /**
   * Fix bullet lists to ensure proper markdown formatting
   */
  private fixBulletLists(text: string): string {
    // Pattern to match bullet lists
    const bulletListPattern = /^(\s*)[-*+]\s*(\*\*[^*]+\*\*:?)\s*(.*)$/gm;

    return text.replace(bulletListPattern, (_match, indent, title, description) => {
      const cleanTitle = title.replace(/\*\*/g, '').trim();
      const formattedTitle = `**${cleanTitle}**`;
      const formattedDescription = description.trim();

      return `${indent}- ${formattedTitle}${formattedDescription ? ': ' + formattedDescription : ''}`;
    });
  }

  /**
   * Fix headers to ensure proper markdown formatting
   */
  private fixHeaders(text: string): string {
    // Ensure headers have proper spacing
    const headerPattern = /^(#{1,6})\s*([^#\n]+)$/gm;

    return text.replace(headerPattern, (_match, hashes, title) => {
      return `${hashes} ${title.trim()}`;
    });
  }

  /**
   * Fix code blocks to ensure proper markdown formatting
   */
  private fixCodeBlocks(text: string): string {
    // Ensure code blocks have proper spacing
    const codeBlockPattern = /```(\w*)\n([\s\S]*?)```/g;

    return text.replace(codeBlockPattern, (_match, language, code) => {
      const trimmedCode = code.trim();
      return `\`\`\`${language}\n${trimmedCode}\n\`\`\``;
    });
  }

  /**
   * Fix inline code to ensure proper markdown formatting
   */
  private fixInlineCode(text: string): string {
    // Ensure inline code has proper backticks
    const inlineCodePattern = /`([^`]+)`/g;

    return text.replace(inlineCodePattern, (_match, code) => {
      return `\`${code.trim()}\``;
    });
  }

  /**
   * Fix bold and italic text formatting
   */
  private fixBoldItalic(text: string): string {
    // Fix bold text - ensure proper ** formatting
    let formatted = text.replace(/\*\*([^*]+)\*\*/g, '**$1**');

    // Fix italic text - ensure proper * formatting
    formatted = formatted.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '*$1*');

    return formatted;
  }

  /**
   * Fix links to ensure proper markdown formatting
   */
  private fixLinks(text: string): string {
    // Fix markdown links
    const linkPattern = /\[([^\]]+)\]\(([^)]+)\)/g;

    return text.replace(linkPattern, (_match, text, url) => {
      return `[${text.trim()}](${url.trim()})`;
    });
  }

  /**
   * Clean up extra whitespace and ensure proper line breaks
   */
  private cleanupWhitespace(text: string): string {
    // Remove excessive line breaks (more than 2 consecutive)
    let cleaned = text.replace(/\n{3,}/g, '\n\n');

    // Ensure proper spacing around lists
    cleaned = cleaned.replace(/(\n)(\d+\.\s)/g, '\n\n$2');
    cleaned = cleaned.replace(/(\n)([-*+]\s)/g, '\n\n$2');

    // Remove trailing whitespace from lines
    cleaned = cleaned.replace(/[ \t]+$/gm, '');

    // Ensure single line break at the end
    cleaned = cleaned.replace(/\n+$/, '\n');

    return cleaned;
  }

  /**
   * Validate markdown formatting
   */
  validateMarkdown(text: string): { isValid: boolean; issues: string[] } {
    const issues: string[] = [];

    // Check for common markdown issues
    if (text.includes('**') && !text.match(/\*\*[^*]+\*\*/)) {
      issues.push('Malformed bold text');
    }

    if (text.includes('```') && !text.match(/```[\s\S]*?```/)) {
      issues.push('Malformed code blocks');
    }

    if (text.includes('[') && !text.match(/\[([^\]]+)\]\(([^)]+)\)/)) {
      issues.push('Malformed links');
    }

    // Check for numbered lists
    const numberedListLines = text.split('\n').filter(line => /^\s*\d+\.\s/.test(line));
    if (numberedListLines.length > 0) {
      // Check if numbered lists have proper formatting
      for (const line of numberedListLines) {
        if (!/^\s*\d+\.\s/.test(line.trim())) {
          issues.push('Malformed numbered list');
          break;
        }
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
    };
  }

  /**
   * Get formatting statistics
   */
  getFormattingStats(text: string): Record<string, number> {
    const stats = {
      totalLength: text.length,
      lines: text.split('\n').length,
      numberedLists: (text.match(/^\s*\d+\.\s/gm) || []).length,
      bulletLists: (text.match(/^\s*[-*+]\s/gm) || []).length,
      headers: (text.match(/^#{1,6}\s/gm) || []).length,
      codeBlocks: (text.match(/```/g) || []).length / 2,
      inlineCode: (text.match(/`[^`]+`/g) || []).length,
      boldText: (text.match(/\*\*[^*]+\*\*/g) || []).length,
      italicText: (text.match(/(?<!\*)\*[^*]+\*(?!\*)/g) || []).length,
      links: (text.match(/\[([^\]]+)\]\(([^)]+)\)/g) || []).length,
    };

    return stats;
  }
}
