import { test } from 'tap';
import { build } from '../../../../../src/app';
import { FastifyInstance } from 'fastify';

test('Refactored Agent Routes', async (t) => {
  let app: FastifyInstance;

  t.beforeEach(async () => {
    app = await build({ logger: false });
  });

  t.afterEach(async () => {
    await app.close();
  });

  t.test('POST /v1/external/agent/invoke - should have correct schema', async (st) => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/agent/invoke',
      headers: {
        'content-type': 'application/json',
        'x-api-key': 'test-key', // This will fail auth but we're testing schema
      },
      payload: {
        agent_name: 'test_agent',
        input: 'test input',
        context: {
          sessionId: 'test-session',
        },
      },
    });

    // Should get 401 due to invalid API key, but this confirms the route exists
    // and the schema is being applied (not a 404)
    st.ok([401, 400, 500].includes(response.statusCode), 'Route exists and processes request');
  });

  t.test('POST /v1/external/agent/stream - should have correct schema', async (st) => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/agent/stream',
      headers: {
        'content-type': 'application/json',
        'x-api-key': 'test-key', // This will fail auth but we're testing schema
      },
      payload: {
        agent_name: 'test_agent',
        input: 'test input',
        context: {
          sessionId: 'test-session',
        },
      },
    });

    // Should get 401 due to invalid API key, but this confirms the route exists
    // and the schema is being applied (not a 404)
    st.ok([401, 400, 500].includes(response.statusCode), 'Route exists and processes request');
  });

  t.test('POST /v1/external/agent/invoke - should validate required fields', async (st) => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/agent/invoke',
      headers: {
        'content-type': 'application/json',
        'x-api-key': 'test-key',
      },
      payload: {
        // Missing required fields
        input: 'test input',
      },
    });

    // Should get validation error for missing agent_name
    st.equal(response.statusCode, 400, 'Returns 400 for validation error');
  });

  t.test('POST /v1/external/agent/stream - should validate required fields', async (st) => {
    const response = await app.inject({
      method: 'POST',
      url: '/v1/external/agent/stream',
      headers: {
        'content-type': 'application/json',
        'x-api-key': 'test-key',
      },
      payload: {
        // Missing required fields
        agent_name: 'test_agent',
      },
    });

    // Should get validation error for missing input
    st.equal(response.statusCode, 400, 'Returns 400 for validation error');
  });
});
